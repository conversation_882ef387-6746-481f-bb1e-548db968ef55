cargo :    Compiling achidas v0.1.0 (D:\workspace\.rust\achidas)
At line:1 char:1
+ cargo build 2>&1 | tee build_log.txt
+ ~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (   Compiling ac...\.rust\achidas):String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
error[E0432]: unresolved import `crate::utils::auth`
 --> src\controllers\applications.rs:8:12
  |
8 |     utils::auth::Claims,
  |            ^^^^ could not find `auth` in `utils`

warning: unused imports: `AddDatabaseReadReplicaRequest`, `CreateDatabaseConnectionPoolRequest`, 
`CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, 
`CreateStorageGatewayExportRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, 
`StartDatabaseMigrationRequest`, `StartDatabaseVersionUpgradeRequest`, `UpdateDatabaseConnectionPoolRequest`, 
`UpdateDatabaseConnectorRequest`, `UpdateDatabaseTopicRequest`, `VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, 
`VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, `VultrDatabaseDB`, 
`VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, `VultrDatabaseReadReplica`, 
`VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrStorageGatewayExportConfig`, 
`VultrVFSAttachment`, and `models::{
             }`
  --> src\controllers\vultr.rs:3:5
   |
3  | /     models::{
4  | |     },
   | |_____^
...
34 |           SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,
   |                                      ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
35 |           VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,
   |           ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
36 |           VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,
   |           ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
37 |           CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
38 |           VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
39 |           VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
40 |           AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
41 |           RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
42 |           CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
43 |           VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
55 |           CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
56 |           CreateUserRequest, UpdateUserRequest, VultrVFS, CreateVFSRequest,
57 |           UpdateVFSRequest, VultrVFSAttachment, VultrVFSRegion, BareMetalActionRequest,
   |                             ^^^^^^^^^^^^^^^^^^
   |
   = note: `#[warn(unused_imports)]` on by default

warning: unused import: `EnvironmentGroup`
 --> src\controllers\environment.rs:4:49
  |
4 |     services::environment::{EnvironmentService, EnvironmentGroup},
  |                                                 ^^^^^^^^^^^^^^^^

warning: unused import: `self`
  --> src\controllers\logs.rs:13:23
   |
13 | use futures::stream::{self, Stream};
   |                       ^^^^

warning: unused import: `crate::with_circuit_breaker`
   --> src\controllers\logs.rs:305:13
    |
305 |         use crate::with_circuit_breaker;
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::with_circuit_breaker`
   --> src\controllers\logs.rs:401:13
    |
401 |         use crate::with_circuit_breaker;
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `StatusCode`
 --> src\controllers\webhooks.rs:9:23
  |
9 |     http::{HeaderMap, StatusCode},
  |                       ^^^^^^^^^^

warning: unused import: `error`
  --> src\controllers\webhooks.rs:14:15
   |
14 | use tracing::{error, info, instrument, warn};
   |               ^^^^^

warning: unused import: `serde_json::json`
  --> src\controllers\mod.rs:20:5
   |
20 | use serde_json::json;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `ServiceResult`
 --> src\infrastructure\circuit_breaker.rs:2:37
  |
2 | use crate::services::{ServiceError, ServiceResult};
  |                                     ^^^^^^^^^^^^^

warning: unused import: `error`
 --> src\infrastructure\state_machine.rs:3:27
  |
3 | use tracing::{info, warn, error};
  |                           ^^^^^

warning: unused imports: `clock::DefaultClock` and `state::InMemoryState`
 --> src\infrastructure\rate_limiter.rs:1:62
  |
1 | use governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};
  |                                                              ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
 --> src\infrastructure\metrics.rs:4:21
  |
4 | use tracing::{info, error};
  |                     ^^^^^

warning: unused import: `std::collections::VecDeque`
 --> src\infrastructure\chunk_processor.rs:7:5
  |
7 | use std::collections::VecDeque;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\middleware\auth.rs:14:15
   |
14 | use tracing::{error, warn};
   |               ^^^^^

warning: unused imports: `HeaderValue` and `response::Response`
 --> src\middleware\cors.rs:2:20
  |
2 |     http::{header, HeaderValue, Method},
  |                    ^^^^^^^^^^^
3 |     response::Response,
  |     ^^^^^^^^^^^^^^^^^^

warning: unused imports: `HeaderMap` and `extract::Query`
  --> src\middleware\mod.rs:7:46
   |
7  |     http::{Request, StatusCode, HeaderValue, HeaderMap},
   |                                              ^^^^^^^^^
...
12 |     extract::Query,
   |     ^^^^^^^^^^^^^^

warning: unused import: `bson::oid::ObjectId`
 --> src\models\mod.rs:1:5
  |
1 | use bson::oid::ObjectId;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\models\mod.rs:2:14
  |
2 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: unused import: `uuid::Uuid`
 --> src\models\mod.rs:4:5
  |
4 | use uuid::Uuid;
  |     ^^^^^^^^^^

warning: unused import: `validator::Validate`
 --> src\models\user.rs:4:5
  |
4 | use validator::Validate;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `validator::Validate`
 --> src\models\instance.rs:4:5
  |
4 | use validator::Validate;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `Level`
  --> src\observability\mod.rs:13:21
   |
13 | use tracing::{info, Level};
   |                     ^^^^^

warning: unused import: `error`
  --> src\services\auth.rs:15:15
   |
15 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused imports: `DateTime` and `Utc`
  --> src\services\billing.rs:14:14
   |
14 | use chrono::{DateTime, Utc, Datelike, Timelike};
   |              ^^^^^^^^  ^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\blueprint.rs:17:5
   |
17 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `with_circuit_breaker`
  --> src\services\build.rs:15:5
   |
15 |     with_circuit_breaker,
   |     ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\build.rs:21:5
   |
21 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::process::Stdio`
  --> src\services\build.rs:23:5
   |
23 | use std::process::Stdio;
   |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `tokio::process::Command`
  --> src\services\build.rs:24:5
   |
24 | use tokio::process::Command;
   |     ^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `AsyncBufReadExt` and `BufReader`
  --> src\services\build.rs:25:17
   |
25 | use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};
   |                 ^^^^^^^^^^^^^^^                ^^^^^^^^^

warning: unused import: `warn`
  --> src\services\build.rs:26:40
   |
26 | use tracing::{error, info, instrument, warn};
   |                                        ^^^^

warning: unused import: `uuid::Uuid`
  --> src\services\build.rs:27:5
   |
27 | use uuid::Uuid;
   |     ^^^^^^^^^^

warning: unused imports: `AutoScalingConfig`, `BlueprintServiceType as ServiceType`, `DeploymentTrigger`, `Environment`, and 
`with_circuit_breaker`
  --> src\services\deployment.rs:10:52
   |
10 |         Application, Deployment, DeploymentStatus, DeploymentTrigger, ApplicationStatus,
   |                                                    ^^^^^^^^^^^^^^^^^
11 |         CreateApplicationRequest, ApplicationResponse, DeploymentResponse, TriggerDeploymentRequest,
12 |         Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,
   |         ^^^^^^^^^^^                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
13 |         AutoScalingConfig, PaginationQuery
   |         ^^^^^^^^^^^^^^^^^
...
17 |     with_circuit_breaker,
   |     ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\deployment.rs:23:5
   |
23 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\deployment.rs:24:15
   |
24 | use tracing::{error, info, instrument, warn};
   |               ^^^^^

warning: unused import: `DiskPagination as Pagination`
 --> src\services\disk.rs:8:46
  |
8 |     models::{Disk, DiskStatus, DiskSnapshot, DiskPagination as Pagination},
  |                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `error` and `warn`
  --> src\services\disk.rs:18:15
   |
18 | use tracing::{error, info, instrument, warn};
   |               ^^^^^                    ^^^^

warning: unused imports: `error` and `warn`
  --> src\services\domain.rs:17:15
   |
17 | use tracing::{error, info, instrument, warn};
   |               ^^^^^                    ^^^^

warning: unused import: `Environment`
 --> src\services\environment.rs:4:27
  |
4 |     models::{Application, Environment},
  |                           ^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\environment.rs:18:15
   |
18 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused imports: `GitCommit` and `GitUser`
 --> src\services\git.rs:6:53
  |
6 |         CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser
  |                                                     ^^^^^^^^^  ^^^^^^^

warning: unused import: `anyhow::Result`
  --> src\services\git.rs:10:5
   |
10 | use anyhow::Result;
   |     ^^^^^^^^^^^^^^

warning: unused import: `chrono::Utc`
  --> src\services\git.rs:12:5
   |
12 | use chrono::Utc;
   |     ^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\git.rs:16:5
   |
16 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `error` and `info`
  --> src\services\git.rs:17:15
   |
17 | use tracing::{error, info, instrument, warn};
   |               ^^^^^  ^^^^

warning: unused import: `error`
  --> src\services\instance.rs:14:15
   |
14 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `CreateInstanceRequest`
 --> src\services\intelligent_hosting.rs:4:26
  |
4 |     vultr::{VultrClient, CreateInstanceRequest},
  |                          ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `anyhow::Result`
 --> src\services\intelligent_hosting.rs:6:5
  |
6 | use anyhow::Result;
  |     ^^^^^^^^^^^^^^

warning: unused imports: `error` and `warn`
 --> src\services\intelligent_hosting.rs:9:33
  |
9 | use tracing::{info, instrument, warn, error};
  |                                 ^^^^  ^^^^^

warning: unused imports: `Duration` and `sleep`
  --> src\services\intelligent_hosting.rs:10:19
   |
10 | use tokio::time::{sleep, Duration};
   |                   ^^^^^  ^^^^^^^^

warning: unused import: `anyhow::Result`
 --> src\services\kubernetes_deployment.rs:6:5
  |
6 | use anyhow::Result;
  |     ^^^^^^^^^^^^^^

warning: unused import: `warn`
 --> src\services\kubernetes_deployment.rs:9:33
  |
9 | use tracing::{info, instrument, warn};
  |                                 ^^^^

warning: unused import: `crate::controllers::ControllerError`
  --> src\services\mod.rs:16:5
   |
16 | use crate::controllers::ControllerError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `DateTime`
 --> src\utils\mod.rs:3:14
  |
3 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `Response`
 --> src\vultr\mod.rs:3:42
  |
3 | use reqwest::{header::HeaderMap, Client, Response};
  |                                          ^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\vultr\mod.rs:4:13
  |
4 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
 --> src\vultr\mod.rs:7:5
  |
7 | use futures::TryStreamExt;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\vultr\models.rs:1:14
  |
1 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: use of deprecated function `base64::decode`: Use Engine::decode
   --> src\services\blueprint.rs:441:37
    |
441 |         let content_bytes = base64::decode(content_b64.replace('\n', ""))
    |                                     ^^^^^^
    |
    = note: `#[warn(deprecated)]` on by default

warning: use of deprecated function `base64::decode`: Use Engine::decode
   --> src\services\blueprint.rs:480:37
    |
480 |         let content_bytes = base64::decode(content_b64)
    |                                     ^^^^^^

warning: unused variable: `state`
   --> src\controllers\auth.rs:113:11
    |
113 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `state`
   --> src\controllers\intelligent_hosting.rs:325:11
    |
325 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src\controllers\intelligent_hosting.rs:368:11
    |
368 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `claims`
   --> src\controllers\logs.rs:117:5
    |
117 |     claims: Claims,
    |     ^^^^^^ help: if this is intentional, prefix it with an underscore: `_claims`

warning: unused variable: `signature`
  --> src\controllers\webhooks.rs:23:9
   |
23 |     let signature = headers
   |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_signature`

warning: unused variable: `token`
  --> src\controllers\webhooks.rs:68:9
   |
68 |     let token = headers
   |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_token`

warning: unused variable: `state`
   --> src\controllers\webhooks.rs:201:11
    |
201 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `chunk`
   --> src\infrastructure\rate_limiter.rs:168:13
    |
168 |         for chunk in requests.chunks(5) {
    |             ^^^^^ help: if this is intentional, prefix it with an underscore: `_chunk`

warning: unused variable: `chunk`
   --> src\infrastructure\rate_limiter.rs:184:13
    |
184 |         for chunk in requests.chunks(10) {
    |             ^^^^^ help: if this is intentional, prefix it with an underscore: `_chunk`

error[E0308]: mismatched types
  --> src\infrastructure\database_indexes.rs:23:13
   |
23 |             self.create_application_indexes(),
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected future, found a different future
   |
   = help: consider `await`ing on both `Future`s
   = note: distinct uses of `impl Trait` result in different opaque types

warning: unused variable: `collection`
  --> src\infrastructure\database_indexes.rs:49:13
   |
49 |         let collection = self.database.collection::<Document>("users");
   |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
  --> src\infrastructure\database_indexes.rs:94:13
   |
94 |         let collection = self.database.collection::<Document>("applications");
   |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
   --> src\infrastructure\database_indexes.rs:156:13
    |
156 |         let collection = self.database.collection::<Document>("deployments");
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
   --> src\infrastructure\database_indexes.rs:218:13
    |
218 |         let collection = self.database.collection::<Document>("build_jobs");
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
   --> src\infrastructure\database_indexes.rs:285:13
    |
285 |         let collection = self.database.collection::<Document>("environment_groups");
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `index_doc`
   --> src\infrastructure\database_indexes.rs:507:36
    |
507 |                     while let Some(index_doc) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
    |                                    ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_index_doc`

warning: unused variable: `method`
   --> src\observability\mod.rs:108:9
    |
108 |     let method = req.method().clone();
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_method`

warning: unused variable: `path`
   --> src\observability\mod.rs:109:9
    |
109 |     let path = req.uri().path().to_string();
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_path`

warning: unused variable: `duration`
   --> src\observability\mod.rs:113:9
    |
113 |     let duration = start.elapsed();
    |         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_duration`

warning: unused variable: `status`
   --> src\observability\mod.rs:114:9
    |
114 |     let status = response.status().as_u16();
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_status`

warning: unused variable: `blueprint_id`
   --> src\services\blueprint.rs:628:43
    |
628 |     async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {
    |                                           ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_blueprint_id`

warning: unused variable: `name`
   --> src\services\blueprint.rs:628:67
    |
628 |     async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {
    |                                                                   ^^^^ help: if this is intentional, prefix it with an 
underscore: `_name`

warning: unused variable: `blueprint_id`
   --> src\services\blueprint.rs:641:36
    |
641 |     async fn create_service(&self, blueprint_id: ObjectId, config: &serde_json::Value) -> ServiceResult<String> {
    |                                    ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_blueprint_id`

warning: unused variable: `metadata`
   --> src\services\build.rs:801:25
    |
801 |                     let metadata = entry.metadata().await
    |                         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_metadata`

warning: unused variable: `request`
   --> src\services\deployment.rs:229:9
    |
229 |         request: TriggerDeploymentRequest,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_request`

warning: unused variable: `update_result`
   --> src\services\deployment.rs:292:13
    |
292 |         let update_result = self.deployments
    |             ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_update_result`

warning: unused variable: `deployment_id`
   --> src\services\deployment.rs:352:9
    |
352 |         deployment_id: ObjectId,
    |         ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_deployment_id`

warning: unused variable: `build_job_id`
   --> src\services\deployment.rs:353:9
    |
353 |         build_job_id: String,
    |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_build_job_id`

warning: unused variable: `state_manager`
   --> src\services\deployment.rs:354:13
    |
354 |         mut state_manager: DeploymentStateManager,
    |             ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_state_manager`

warning: variable does not need to be mutable
   --> src\services\deployment.rs:354:9
    |
354 |         mut state_manager: DeploymentStateManager,
    |         ----^^^^^^^^^^^^^
    |         |
    |         help: remove this `mut`
    |
    = note: `#[warn(unused_mut)]` on by default

warning: unused variable: `application`
   --> src\services\deployment.rs:434:46
    |
434 |     async fn determine_build_priority(&self, application: &Application) -> crate::models::BuildPriority {
    |                                              ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_application`

warning: unused variable: `deployment`
   --> src\services\deployment.rs:873:13
    |
873 |         let deployment = self.deployments
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_deployment`

warning: unused variable: `request`
   --> src\services\disk.rs:396:73
    |
396 |     async fn update_infrastructure_disk(&self, infrastructure_id: &str, request: &UpdateDiskRequest) -> 
ServiceResult<()> {
    |                                                                         ^^^^^^^ help: if this is intentional, prefix 
it with an underscore: `_request`

warning: unused variable: `application_id`
   --> src\services\git.rs:249:42
    |
249 |     async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> 
ServiceResult<Strin...
    |                                          ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_application_id`

warning: unused variable: `repository`
   --> src\services\git.rs:249:69
    |
249 | ...plication_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_repository`

warning: unused variable: `webhook_secret`
   --> src\services\git.rs:249:94
    |
249 | ...sitory: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                         ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_webhook_secret`

warning: unused variable: `application_id`
   --> src\services\git.rs:257:45
    |
257 | ...ucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> 
ServiceResult<String> {
    |                         ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_application_id`

warning: unused variable: `repository`
   --> src\services\git.rs:257:72
    |
257 | ...plication_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_repository`

warning: unused variable: `webhook_secret`
   --> src\services\git.rs:257:97
    |
257 | ...sitory: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                         ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_webhook_secret`

warning: unused variable: `applications`
   --> src\services\git.rs:278:13
    |
278 |         let applications = self.applications
    |             ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_applications`

warning: variable does not need to be mutable
   --> src\services\git.rs:286:13
    |
286 |         let mut triggered_deployments = Vec::new();
    |             ----^^^^^^^^^^^^^^^^^^^^^
    |             |
    |             help: remove this `mut`

warning: unused variable: `setup_script`
   --> src\services\intelligent_hosting.rs:311:13
    |
311 |         let setup_script = match pool_type {
    |             ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_setup_script`

warning: unused variable: `config`
    --> src\services\intelligent_hosting.rs:1328:53
     |
1328 |     async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {
     |                                                     ^^^^^^ help: if this is intentional, prefix it with an 
underscore: `_config`

warning: unused variable: `server_id`
    --> src\services\intelligent_hosting.rs:1328:67
     |
1328 |     async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {
     |                                                                   ^^^^^^^^^ help: if this is intentional, prefix it 
with an underscore: `_server_id`

warning: unused variable: `specs`
    --> src\services\intelligent_hosting.rs:1333:56
     |
1333 |     async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> 
ServiceResult<String> {
     |                                                        ^^^^^ help: if this is intentional, prefix it with an 
underscore: `_specs`

warning: unused variable: `region`
    --> src\services\intelligent_hosting.rs:1333:84
     |
1333 |     async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> 
ServiceResult<String> {
     |                                                                                    ^^^^^^ help: if this is 
intentional, prefix it with an underscore: `_region`

warning: unused variable: `specs`
    --> src\services\intelligent_hosting.rs:1338:61
     |
1338 |     async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> 
ServiceResult<String> {
     |                                                             ^^^^^ help: if this is intentional, prefix it with an 
underscore: `_specs`

warning: unused variable: `region`
    --> src\services\intelligent_hosting.rs:1338:89
     |
1338 |     async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> 
ServiceResult<String> {
     |                                                                                         ^^^^^^ help: if this is 
intentional, prefix it with an underscore: `_region`

warning: unused variable: `response`
   --> src\vultr\mod.rs:251:13
    |
251 |         let response = self.client.delete(&url).await?;
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_response`

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {list_applications}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:62:37
    |
62  |         .route("/applications", get(controllers::applications::list_applications))
    |                                 --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<Arc<AppState>>, {type error}, ...) -> ... {list_applications}`
    |                                 |
    |                                 required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `IntoHandler<H, T, S>` implements `Handler<T, S>`
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
              `axum_extra::handler::Or<L, R, Lt, Rt, S>` implements `Handler<(M, Lt, Rt), S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-7a22855f9b505163.long-type-10898613523476983900.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for 
more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {create_application}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:63:38
    |
63  |         .route("/applications", post(controllers::applications::create_application))
    |                                 ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<Arc<AppState>>, ..., ...) -> ... {create_application}`
    |                                 |
    |                                 required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `IntoHandler<H, T, S>` implements `Handler<T, S>`
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
              `axum_extra::handler::Or<L, R, Lt, Rt, S>` implements `Handler<(M, Lt, Rt), S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-7a22855f9b505163.long-type-8993199981927314322.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for 
more info)

error[E0277]: the trait bound `fn(State<Arc<...>>, ..., ...) -> ... {get_application}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:64:45
    |
64  |         .route("/applications/:app_id", get(controllers::applications::get_application))
    |                                         --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is 
not implemented for fn item `fn(State<Arc<AppState>>, {type error}, ...) -> ... {get_application}`
    |                                         |
    |                                         required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `IntoHandler<H, T, S>` implements `Handler<T, S>`
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
              `axum_extra::handler::Or<L, R, Lt, Rt, S>` implements `Handler<(M, Lt, Rt), S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-7a22855f9b505163.long-type-4873468787472527508.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for 
more info)

error[E0277]: the trait bound `fn(..., ..., ..., ...) -> ... {update_application}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:65:45
    |
65  |         .route("/applications/:app_id", put(controllers::applications::update_application))
    |                                         --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is 
not implemented for fn item `fn(State<Arc<AppState>>, ..., ..., ...) -> ... {update_application}`
    |                                         |
    |                                         required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `IntoHandler<H, T, S>` implements `Handler<T, S>`
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
              `axum_extra::handler::Or<L, R, Lt, Rt, S>` implements `Handler<(M, Lt, Rt), S>`
note: required by a bound in `put`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:444:1
    |
444 | top_level_handler_fn!(put, PUT);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `put`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-7a22855f9b505163.long-type-3054436078499453833.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for 
more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {delete_application}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:66:48
    |
66  |         .route("/applications/:app_id", delete(controllers::applications::delete_application))
    |                                         ------ ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` 
is not implemented for fn item `fn(State<Arc<AppState>>, ..., ...) -> ... {delete_application}`
    |                                         |
    |                                         required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `IntoHandler<H, T, S>` implements `Handler<T, S>`
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
              `axum_extra::handler::Or<L, R, Lt, Rt, S>` implements `Handler<(M, Lt, Rt), S>`
note: required by a bound in `delete`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:438:1
    |
438 | top_level_handler_fn!(delete, DELETE);
    | ^^^^^^^^^^^^^^^^^^^^^^------^^^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `delete`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-7a22855f9b505163.long-type-8451852944859962563.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for 
more info)

error[E0277]: the trait bound `fn(..., ..., ..., ...) -> ... {trigger_deployment}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:67:53
    |
67  |         .route("/applications/:app_id/deploy", post(controllers::applications::trigger_deployment))
    |                                                ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait 
`Handler<_, _>` is not implemented for fn item `fn(State<Arc<AppState>>, ..., ..., ...) -> ... {trigger_deployment}`
    |                                                |
    |                                                required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `IntoHandler<H, T, S>` implements `Handler<T, S>`
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
              `axum_extra::handler::Or<L, R, Lt, Rt, S>` implements `Handler<(M, Lt, Rt), S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-7a22855f9b505163.long-type-7240029956822462966.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for 
more info)

error[E0277]: the trait bound `fn(State<...>, ..., ..., ...) -> ... {list_deployments}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:68:57
    |
68  |         .route("/applications/:app_id/deployments", get(controllers::applications::list_deployments))
    |                                                     --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait 
`Handler<_, _>` is not implemented for fn item `fn(State<Arc<AppState>>, ..., ..., ...) -> ... {list_deployments}`
    |                                                     |
    |                                                     required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `IntoHandler<H, T, S>` implements `Handler<T, S>`
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
              `axum_extra::handler::Or<L, R, Lt, Rt, S>` implements `Handler<(M, Lt, Rt), S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-7a22855f9b505163.long-type-5848312023495339187.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for 
more info)

error[E0277]: the trait bound `fn(State<Arc<...>>, ..., ...) -> ... {get_deployment}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:69:72
    |
69  |         .route("/applications/:app_id/deployments/:deployment_id", get(controllers::applications::get_deployment))
    |                                                                    --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the 
trait `Handler<_, _>` is not implemented for fn item `fn(State<Arc<AppState>>, {type error}, ...) -> ... {get_deployment}`
    |                                                                    |
    |                                                                    required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `IntoHandler<H, T, S>` implements `Handler<T, S>`
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
              `axum_extra::handler::Or<L, R, Lt, Rt, S>` implements `Handler<(M, Lt, Rt), S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-7a22855f9b505163.long-type-5768544679488790517.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for 
more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {rollback_deployment}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:70:82
    |
70  |         .route("/applications/:app_id/deployments/:deployment_id/rollback", 
post(controllers::applications::rollback_deployment))
    |                                                                             ---- 
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn item 
`fn(State<Arc<AppState>>, ..., ...) -> ... {rollback_deployment}`
    |                                                                             |
    |                                                                             required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `IntoHandler<H, T, S>` implements `Handler<T, S>`
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
              `axum_extra::handler::Or<L, R, Lt, Rt, S>` implements `Handler<(M, Lt, Rt), S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-7a22855f9b505163.long-type-18420739417477208097.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for 
more info)

warning: use of deprecated method `chrono::DateTime::<Tz>::timestamp_nanos`: use `timestamp_nanos_opt()` instead
  --> src\utils\mod.rs:43:9
   |
43 |     now.timestamp_nanos().hash(&mut hasher);
   |         ^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
   --> src\controllers\logs.rs:307:13
    |
307 |         use futures::TryStreamExt;
    |             ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
   --> src\controllers\logs.rs:403:13
    |
403 |         use futures::TryStreamExt;
    |             ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
  --> src\services\blueprint.rs:14:5
   |
14 | use futures::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
  --> src\services\build.rs:19:5
   |
19 | use futures::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
  --> src\services\deployment.rs:21:5
   |
21 | use futures::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
  --> src\services\domain.rs:14:5
   |
14 | use futures::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused variable: `limiter`
   --> src\infrastructure\rate_limiter.rs:260:13
    |
260 |         let limiter = self.limiters
    |             ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_limiter`

warning: unused variable: `handle`
  --> src\observability\mod.rs:82:9
   |
82 |     let handle = builder
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_handle`

Some errors have detailed explanations: E0277, E0308, E0432.
For more information about an error, try `rustc --explain E0277`.
warning: `achidas` (lib) generated 117 warnings
error: could not compile `achidas` (lib) due to 11 previous errors; 117 warnings emitted
